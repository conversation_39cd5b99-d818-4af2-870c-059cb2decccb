import { type NextRequest, NextResponse } from 'next/server';
import { getDynamicTable, isDrizzleTable, validateDatabaseCode } from '@/lib/drizzleTableMapping';
import { getDatabaseConfig } from '@/lib/configCache';
import { getDatabaseAccessLevel } from '@/lib/permissions';
import { checkPermissions } from '@/lib/server/permissions';
import { validatePaginationParams, buildPaginationResponse } from '@/lib/globalPagination';
import { buildDrizzleWhere, buildDrizzleOrderBy } from '@/lib/server/buildDrizzleWhere';
import { db } from '@/lib/db-server';
import { count, desc, asc } from 'drizzle-orm';

export const dynamic = 'force-dynamic';

/**
 * GET /api/data/[database]
 * 获取数据库列表数据 - 基于Drizzle ORM的新实现
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    const { searchParams } = new URL(request.url);

    console.log('[Data API] 接收到列表数据请求:', {
      database,
      params: Object.fromEntries(searchParams.entries())
    });

    // 验证数据库代码
    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    // 权限检查
    const requiredLevel = await getDatabaseAccessLevel(database) as "free" | "premium" | "enterprise";
    const hasAccess = await checkPermissions(requiredLevel);
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      );
    }

    // 获取分页参数
    const requestedPage = parseInt(searchParams.get('page') || '1');
    const requestedLimit = parseInt(searchParams.get('limit') || '20');
    const { page, limit } = validatePaginationParams(requestedPage, requestedLimit);

    // 获取排序参数
    const sortBy = searchParams.get('sortBy') || '';
    const sortOrder = (searchParams.get('sortOrder') || 'desc') as 'asc' | 'desc';

    // 获取配置
    const config = await getDatabaseConfig(database);
    const visibleFields = config.fields.filter(f => f.isVisible);
    const sortableFields = config.fields.filter(f => f.isSortable);

    // 获取动态表
    const table = await getDynamicTable(database);
    if (!isDrizzleTable(table)) {
      return NextResponse.json(
        { success: false, error: '模型未找到或无效' },
        { status: 500 }
      );
    }

    // 获取表列信息
    const tableColumns: Record<string, any> = {};
    Object.keys(table).forEach(key => {
      if (typeof table[key] === 'object' && table[key].name) {
        tableColumns[key] = table[key];
      }
    });

    // 构建WHERE条件
    const where = buildDrizzleWhere(searchParams, config, tableColumns);

    // 构建排序条件
    const orderByConditions = buildDrizzleOrderBy(sortBy, sortOrder, tableColumns, config);
    
    // 构建排序子句
    const orderByClause = [];
    if (orderByConditions.length > 0) {
      orderByConditions.forEach(orderCondition => {
        const column = tableColumns[orderCondition.field];
        if (column) {
          orderByClause.push(orderCondition.order === 'asc' ? asc(column) : desc(column));
        }
      });
    }
    
    // 如果没有排序条件，使用默认排序
    if (orderByClause.length === 0) {
      if (config.defaultSort && Array.isArray(config.defaultSort)) {
        config.defaultSort.forEach((sort: any) => {
          const column = tableColumns[sort.field];
          if (column) {
            orderByClause.push(sort.order === 'asc' ? asc(column) : desc(column));
          }
        });
      }
      
      // 最后回退到ID排序
      if (orderByClause.length === 0 && tableColumns.id) {
        orderByClause.push(desc(tableColumns.id));
      }
    }

    // 执行查询
    const offset = (page - 1) * limit;
    
    // 查询数据
    let baseQuery = db.select().from(table);
    if (where) {
      baseQuery = baseQuery.where(where);
    }
    if (orderByClause.length > 0) {
      baseQuery = baseQuery.orderBy(...orderByClause);
    }
    
    const data = await baseQuery.limit(limit).offset(offset);

    // 查询总数
    let countQuery = db.select({ count: count() }).from(table);
    if (where) {
      countQuery = countQuery.where(where);
    }
    const [{ count: totalCount }] = await countQuery;

    // 过滤返回的字段，只返回可见字段
    const filteredData = data.map(item => {
      const filtered: Record<string, any> = { id: item.id };
      visibleFields.forEach(fieldConfig => {
        if (item.hasOwnProperty(fieldConfig.fieldName)) {
          filtered[fieldConfig.fieldName] = item[fieldConfig.fieldName];
        }
      });
      return filtered;
    });

    console.log('[Data API] 查询完成:', {
      database,
      dataCount: filteredData.length,
      total: totalCount,
      page,
      limit
    });

    return NextResponse.json({
      success: true,
      data: filteredData,
      pagination: buildPaginationResponse(page, limit, totalCount),
      config: {
        fields: config.fields,
        database: database,
      },
    });

  } catch (error) {
    console.error('[Data API] 错误:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取数据失败',
      data: [],
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
    }, { status: 500 });
  }
}

/**
 * POST /api/data/[database]
 * 支持更复杂的查询参数
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;
    const body = await request.json();
    
    const {
      filters = {},
      page = 1,
      limit = 20,
      sortBy,
      sortOrder = 'desc'
    } = body;

    console.log('[Data API] POST请求:', {
      database,
      filters,
      page,
      limit,
      sortBy,
      sortOrder
    });

    // 验证数据库代码
    const validation = await validateDatabaseCode(database);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: validation.status || 400 }
      );
    }

    // 权限检查
    const requiredLevel = await getDatabaseAccessLevel(database) as "free" | "premium" | "enterprise";
    const hasAccess = await checkPermissions(requiredLevel);
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      );
    }

    // 构建URL搜索参数
    const searchParams = new URLSearchParams();
    searchParams.set('page', page.toString());
    searchParams.set('limit', limit.toString());
    if (sortBy) searchParams.set('sortBy', sortBy);
    searchParams.set('sortOrder', sortOrder);
    
    // 添加过滤器参数
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, v));
        } else {
          searchParams.set(key, String(value));
        }
      }
    });

    // 重用GET方法的逻辑
    const mockRequest = new NextRequest(`http://localhost/api/data/${database}?${searchParams.toString()}`);
    return await GET(mockRequest, { params: Promise.resolve({ database }) });

  } catch (error) {
    console.error('[Data API] POST错误:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取数据失败',
      data: [],
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
    }, { status: 500 });
  }
}
