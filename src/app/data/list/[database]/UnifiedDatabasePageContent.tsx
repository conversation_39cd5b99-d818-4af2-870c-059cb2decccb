'use client';

import { useState, useEffect, useMemo, useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Database } from 'lucide-react';
import { UnifiedFilterPanel } from '@/components/UnifiedFilterPanel';
import AdvancedSearch from '@/components/AdvancedSearch';
import SearchChips from '@/components/SearchChips';
import { DataTable } from '@/components/DataTable';
import { useUnifiedSearch, DatabaseFieldConfig } from '@/lib/hooks/useUnifiedSearch';
import { SearchCondition } from '@/lib/api';
import DatabasePageBreadcrumb from './DatabasePageBreadcrumb';

interface UnifiedDatabasePageContentProps {
  database: string;
}

export default function UnifiedDatabasePageContent({ database }: UnifiedDatabasePageContentProps) {
  const searchParams = useSearchParams();
  const [config, setConfig] = useState<{ fields: DatabaseFieldConfig[] } | null>(null);
  const [configLoading, setConfigLoading] = useState(true);
  const [data, setData] = useState<any[]>([]);
  const [filterOpen, setFilterOpen] = useState(true);
  const initialLoadRef = useRef(false);

  // 使用统一搜索Hook
  const {
    searchState,
    executeSearch,
    updateFromFilterPanel,
    updateFromAdvancedSearch,
    updateSorting,
    updatePagination,
    clearAllConditions
  } = useUnifiedSearch(database, config || undefined);

  // 加载配置
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setConfigLoading(true);
        const response = await fetch(`/api/config/${database}`);
        const result = await response.json();
        
        if (result.success) {
          setConfig(result.data);
        } else {
          console.error('配置加载失败:', result.error);
        }
      } catch (error) {
        console.error('配置加载失败:', error);
      } finally {
        setConfigLoading(false);
      }
    };

    loadConfig();
  }, [database]);

  // 从URL参数初始化搜索条件或加载初始数据
  useEffect(() => {
    if (!config || configLoading || initialLoadRef.current) return;

    const urlFilters: Record<string, any> = {};
    searchParams.forEach((value, key) => {
      // 跳过分页和排序参数
      if (!['page', 'limit', 'sortBy', 'sortOrder'].includes(key)) {
        urlFilters[key] = value;
      }
    });

    initialLoadRef.current = true;

    if (Object.keys(urlFilters).length > 0) {
      // 有URL过滤器，使用过滤器搜索
      updateFromFilterPanel(urlFilters).then(result => {
        if (result?.success) {
          setData(result.data || []);
        }
      });
    } else {
      // 没有URL过滤器，加载初始数据
      loadInitialData();
    }
  }, [config, configLoading]);

  // 加载初始数据
  const loadInitialData = async () => {
    try {
      console.log('[UnifiedDatabasePageContent] 加载初始数据:', database);

      const result = await executeSearch({
        conditions: [],
        page: 1,
        limit: 20
      });

      if (result?.success) {
        setData(result.data || []);
        console.log('[UnifiedDatabasePageContent] 初始数据加载成功:', result.data?.length);
      } else {
        console.error('[UnifiedDatabasePageContent] 初始数据加载失败:', result?.error);
        setData([]);
      }
    } catch (error) {
      console.error('[UnifiedDatabasePageContent] 初始数据加载异常:', error);
      setData([]);
    }
  };

  // 处理Advanced Search条件更新
  const handleAdvancedSearch = async (conditions: SearchCondition[]) => {
    try {
      const result = await updateFromAdvancedSearch(conditions);
      if (result?.success) {
        setData(result.data || []);
      }
    } catch (error) {
      console.error('高级搜索失败:', error);
      setData([]);
    }
  };

  // 处理Advanced Search清除
  const handleAdvancedSearchClear = async () => {
    try {
      const result = await clearAllConditions();
      if (result?.success) {
        setData(result.data || []);
      }
    } catch (error) {
      console.error('清除搜索条件失败:', error);
      setData([]);
    }
  };

  // 处理排序变化
  const handleSortChange = async (sortBy: string, sortOrder: 'asc' | 'desc') => {
    try {
      const result = await updateSorting(sortBy, sortOrder);
      if (result?.success) {
        setData(result.data || []);
      }
    } catch (error) {
      console.error('排序失败:', error);
    }
  };

  // 处理分页变化
  const handlePageChange = async (direction: 'prev' | 'next') => {
    const currentPage = searchState.pagination.page;
    const newPage = direction === 'next' ? currentPage + 1 : currentPage - 1;
    try {
      const result = await updatePagination(newPage);
      if (result?.success) {
        setData(result.data || []);
      }
    } catch (error) {
      console.error('分页失败:', error);
    }
  };

  // 处理搜索标签移除
  const handleChipRemove = async (chipId: string) => {
    // 解析chipId来确定要移除的条件
    if (chipId.startsWith('filter_')) {
      const fieldName = chipId.replace('filter_', '');
      const newFilters: Record<string, any> = {}; // Start with empty filters since we don't store filter state directly
      delete newFilters[fieldName];
      await updateFromFilterPanel(newFilters);
    } else if (chipId.startsWith('condition_')) {
      const conditionId = chipId.replace('condition_', '');
      const newConditions = searchState.conditions.filter(c => c.id !== conditionId);
      await updateFromAdvancedSearch(newConditions);
    }
  };

  // 生成搜索标签 - 修复重复显示问题
  const searchChips = useMemo(() => {
    const chips: Array<{ id: string; label: string; value: string | number | boolean; displayValue: string; type: 'filter' | 'advanced' }> = [];

    if (!config) return chips;

    // 直接从searchState.conditions生成chips，避免重复
    searchState.conditions.forEach(condition => {
      const field = config.fields.find(f => f.fieldName === condition.field);
      if (field && condition.value !== undefined && condition.value !== null && condition.value !== '') {
        // 判断条件类型：如果字段既可筛选又可高级搜索，优先显示为filter类型
        const displayValue = Array.isArray(condition.value) ? condition.value.join(', ') : String(condition.value);
        chips.push({
          id: `condition_${condition.id}`,
          label: field.displayName,
          value: displayValue,
          displayValue: displayValue,
          type: field.isFilterable ? 'filter' : 'advanced'
        });
      }
    });

    return chips;
  }, [searchState.conditions, config]);

  if (configLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading configuration...</p>
        </div>
      </div>
    );
  }

  if (!config) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Database className="h-8 w-8 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-600">Failed to load database configuration</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex">
      {/* Filter Panel */}
      <UnifiedFilterPanel
        database={database}
        config={config}
        isOpen={filterOpen}
        onToggle={() => setFilterOpen(!filterOpen)}
        className="fixed top-16 left-0 bottom-0 z-50 overflow-hidden hidden md:block"
      />

      {/* Breadcrumb Navigation - Responsive to filter panel */}
      <div className={`fixed top-16 right-0 z-40 bg-gray-50 border-b border-gray-100 transition-all duration-300 ${filterOpen ? 'left-72' : 'left-16'} hidden md:block`}>
        <DatabasePageBreadcrumb database={database} />
      </div>

      {/* Mobile Breadcrumb - Full width on mobile */}
      <div className="fixed top-16 left-0 right-0 z-40 bg-gray-50 border-b border-gray-100 md:hidden">
        <DatabasePageBreadcrumb database={database} />
      </div>

      {/* Main Content */}
      <div className={`flex-1 transition-all duration-300 ${filterOpen ? 'ml-72' : 'ml-16'} flex flex-col`} style={{ marginTop: '0px' }}>
        <div className="flex-1 flex flex-col pl-0 pr-6 pt-0 pb-0 space-y-2">
          {/* Header */}
          <div className="flex items-center justify-between px-6 py-2">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {database.toUpperCase()} Database
              </h1>
            </div>

            <div className="flex items-center gap-4">
              <AdvancedSearch
                availableFields={config.fields.filter(f => f.isAdvancedSearchable)}
                currentConditions={searchState.conditions}
                onSearch={handleAdvancedSearch}
                onClear={handleAdvancedSearchClear}
              />
            </div>
          </div>

          {/* Search Chips */}
          {searchChips.length > 0 && (
            <div className="px-6">
              <SearchChips
                chips={searchChips}
                onRemoveChip={handleChipRemove}
                onClearAll={clearAllConditions}
              />
            </div>
          )}

          {/* Data Table */}
          <Card className="flex-1 flex flex-col">
            <CardContent className="p-0 flex-1 flex flex-col">
              <DataTable
                data={data}
                config={config}
                pagination={searchState.pagination}
                onPageChange={handlePageChange}
                onSortChange={handleSortChange}
                loading={searchState.isLoading}
              />
            </CardContent>
          </Card>


        </div>
      </div>
    </div>
  );
}
